package cn.emoney.im.delivery.entity;

import lombok.Data;

/**
 * 发送Link服务的命令请求对象
 *
 * <AUTHOR>
 * @date 2024/04/28 16:22
 **/
@Data
public class UserCmdRequest {

    //构造
    public UserCmdRequest(String nodeId, String group, String uid, Integer fd, String cmd, String data) {
        this.nodeId = nodeId;
        this.group = group;
        this.uid = uid;
        this.fd = fd;
        this.cmd = cmd;
        this.data = data;
    }

   //create class member with String nodeId,String group,String uid,Integer fd,String cmd,String data
    /*节点ID*/
    private String nodeId;
    /*连接组*/
    private String group;
    /*用户ID*/
    private String uid;
    /*连接号*/
    private Integer fd;
    /*命令*/
    private String cmd;
    /*数据*/
    private String data;


    public UserCmdRequest() {

    }
}
