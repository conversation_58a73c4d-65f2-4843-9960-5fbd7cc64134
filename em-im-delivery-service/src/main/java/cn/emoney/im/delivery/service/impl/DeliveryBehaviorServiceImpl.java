package cn.emoney.im.delivery.service.impl;

import cn.emoney.im.delivery.service.DeliveryBehaviorService;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.EntryProcessor;
import com.hazelcast.map.IMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/04/07 13:15
 **/
@Slf4j
@Service
public class DeliveryBehaviorServiceImpl implements DeliveryBehaviorService {

    private final HazelcastInstance hazelcastInstance;

    private static final String hazelcastMapName = "UserActivityCountMap";

    public DeliveryBehaviorServiceImpl(@Qualifier("hazelcastInstance") HazelcastInstance hazelcastInstance) {
        this.hazelcastInstance = hazelcastInstance;
    }

    /***
     * 功能描述:
     * 批量获取用户当天某业务的活动量
     * @Param: [bizKey, uidSet, date]
     * @Return: java.util.Map<java.lang.Long,java.util.concurrent.atomic.AtomicInteger>
     * @Author: tengdengming
     * @Date: 2024/4/7 17:24
     */
    @Override
    public Map<Long, Integer> getUserTodayBusinessCount(String bizKey, Set<Long> uidSet, LocalDate date) {

        IMap<String, AtomicInteger> userActivityCountsMap = hazelcastInstance.getMap(hazelcastMapName);
        Set<String> keys = uidSet.stream()
                .map(userId -> bizKey + ":" + userId + ":" + date)
                .collect(Collectors.toSet());

        Map<String, AtomicInteger> queryBackSet = userActivityCountsMap.getAll(keys);


        Map<Long, Integer> collect = queryBackSet.entrySet().stream()
                .collect(Collectors.toMap(e -> Long.parseLong(e.getKey().split(":")[1]), e -> e.getValue().get()));

        // 补全缺失的用户
        uidSet.forEach(uid -> collect.putIfAbsent(uid, 0));
        return collect;

    }

    /***
     * 功能描述:
     * 批量设置用户当天活动次数0
     * @Param: [bizKey, uidSet, date]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2024/4/7 17:23
     */
    @Override
    public void setUserTodayBusinessCount(String bizKey, Set<Long> uidSet,LocalDate date) {

        IMap<String, AtomicInteger> userActivityCountsMap = hazelcastInstance.getMap(hazelcastMapName);
        IncrementActivityCountProcessor processor = new IncrementActivityCountProcessor();

        Set<String> keys = new HashSet<>();
        for (Long userId : uidSet) {
            keys.add(bizKey+":"+userId + ":" + date);
        }

        Map<String, Object> resultMap = userActivityCountsMap.executeOnKeys(keys, processor);

        for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
            AtomicInteger count = (AtomicInteger) entry.getValue();
            userActivityCountsMap.putTransient(entry.getKey(), count, 172800, TimeUnit.SECONDS);
            log.info("活动次数:uid{},count{}", entry.getKey(),count.get());
            System.out.println(entry.getKey() + " 活动次数: " + count.get());
        }

    }

    /***
     * 功能描述:
     * 活动计数计算处理器
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2024/4/7 17:23
     */
    private static class IncrementActivityCountProcessor implements EntryProcessor<String, AtomicInteger, Object> {
        @Override
        public Object process(Map.Entry<String, AtomicInteger> entry) {
            AtomicInteger count = entry.getValue();
            if (count == null) {
                count = new AtomicInteger(0);
                entry.setValue(count);
            }
            count.incrementAndGet();
            return count;
        }

    }
}
