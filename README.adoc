= IM Demo Module image:http://git.emoney.cn/em-im/em-im-demo/badges/master/build.svg[link="http://git.emoney.cn/em-im/em-im-demo/commits/master",title="build status"]
:page: http://git.emoney.cn/em-im/em-im-demo/tree/master

本项目为 IM 模块开发提供了一系列的示例, 方便开发人员快速构建项目。

== 项目初始化

Maven 镜像::
+
XML 配置
+
[source,xml]
----
<mirrors>
    <mirror>
        <id>em-central</id>
        <mirrorOf>central</mirrorOf>
        <url>https://officerepo.emoney.cn/maven-central/</url>
    </mirror>
</mirrors>
----
+
一键配置
+
[source,shell]
----
# for Windows
curl https://officerepo.emoney.cn/setting/maven/settings.xml -o %HOMEPATH%/.m2/settings.xml
----
+
[source,shell]
----
# for Linux
curl https://officerepo.emoney.cn/setting/maven/settings.xml -o ~/.m2/settings.xml
----

项目配置::
+
主配置文件:: application.yml
+
[source,yaml]
----
spring:
  application:
    name: im-demo # 应用名称 (必填) 用于服务发现、服务识别；同 FeignClient 的 name 属性
----

== 分层结构

.em-im-`module`
* em-im-`module`-api
dto:: 数据传输对象
enums:: 枚举
exception:: 异常
utils:: 工具类(仅限域内数据处理)
service:: 服务接口
client:: 服务接口契约, 用于生成 service 实现

* em-im-`module`-service
config:: 配置
controller:: 主要是对 `api` 模块的服务接口进行实现
service:: 业务逻辑
manager:: 通用业务处理
+
. 对第三方平台封装的层，预处理返回结果及转化异常信息。
. 对 Service 层通用能力的下沉，如缓存方案、中间件通用处理。
. 与 DAO 层交互，对多个 DAO 的组合复用。
entity:: 数据实体
mapper:: 数据持久 (MyBatis)

== 项目发布

TODO 需要与运维沟通

== API 包发布

编辑 `.gitlab-ci.yml` 文件, 添加 `deploy` 阶段

mvn $MAVEN_CLI_OPTS deploy -am --settings ci_settings.xml -pl `em-im-demo-api`
