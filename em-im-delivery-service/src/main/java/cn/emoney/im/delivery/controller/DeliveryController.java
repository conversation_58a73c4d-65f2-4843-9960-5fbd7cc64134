package cn.emoney.im.delivery.controller;

import cn.emoney.im.api.delivery.dto.*;
import cn.emoney.im.common.dto.EmResponse;
import cn.emoney.im.delivery.entity.IMConn;
import cn.emoney.im.delivery.entity.getUserTodayBatchSendCountRequest;
import cn.emoney.im.delivery.service.DeliveryBehaviorService;
import cn.emoney.im.delivery.service.DeliveryService;
import cn.emoney.im.delivery.utils.HttpUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Tcp消息投递服务
 *
 * <AUTHOR>
 * @date 2024/01/11 11:25
 **/
@RestController

public class DeliveryController {

    private final DeliveryService deliveryService;
    private final DeliveryBehaviorService deliveryBehaviorService;
    private final ObjectMapper objectMapper;
    private int MaxBatchCount = 1000;

    public DeliveryController(DeliveryService deliveryService, DeliveryBehaviorService deliveryBehaviorService, ObjectMapper objectMapper) {
        this.deliveryService = deliveryService;
        this.deliveryBehaviorService = deliveryBehaviorService;
        this.objectMapper = objectMapper;
    }

    /**
     * 功能描述:
     * 投递Wsc消息
     * @Param: [tcpMessage]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/4/17 11:11
     */
    @PostMapping("deliveryMessage")
    public DeliveryResponse deliverMessage(@RequestBody TcpMessage tcpMessage){
        return deliveryService.deliveryTcpMessage(tcpMessage);
    }

    /***
     * 功能描述:
     * Wsc群发工具，针对IM单聊群发，多个业务消息体
     * @Param: [tcpMessageList]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/4/17 11:10
     */
    @PostMapping("deliverBatchUserTcpMessage")
    public DeliveryResponse deliverBatchUserTcpMessage(@RequestBody List<TcpMessage> tcpMessageList){
        return (DeliveryResponse) deliveryService.deliveryBatchUserTcpMessage(tcpMessageList);
    }

    @PostMapping("deliverBatchUserTcpMessageIgnoreCount")
    public DeliveryResponse deliverBatchUserTcpMessageIgnoreCount(@RequestBody List<TcpMessage> tcpMessageList){
        return (DeliveryResponse) deliveryService.deliveryBatchUserTcpMessageIgnoreCount(tcpMessageList);
    }

    @GetMapping("getDeliveryStatus")
    public String getDeliveryStatus(String receiptNo){
        return deliveryService.getDeliveryStatus(receiptNo);
    }

    //标记终端连接wsc节点NodeId
    @GetMapping("connectWscDone")
    public String connectWscDone(Long uid,Integer nodeId,Integer dId,Integer fd,String session,HttpServletRequest request){
        //获取请求头中用户的客户端IP地址
        String ip = HttpUtils.getClientIp(request);
        return deliveryService.connectWscDone(uid,nodeId,dId,fd,session,ip);
    }

    //获取用户的Wsc连接节点
    @GetMapping("getUserWscNodeId")
    public List<IMConn> getUserWscNodeId(Long uid){
        return deliveryService.getUserWscConnections(uid);
    }

    @GetMapping("disconnectWscDone")
    public String disconnectWscDone(Long uid,Integer nodeId,Integer dId,Integer fd,String session, HttpServletRequest request){
        //获取请求头中用户的客户端IP地址
        String ip = HttpUtils.getClientIp(request);
        deliveryService.closeWscDone(new IMConn(uid,nodeId,dId,fd,session,ip));
        return "ok";
    }

    @GetMapping("writeHZkey")
    public String writeHZkey(String key, String value){
        return deliveryService.writeHZkey(key,value);
    }

    @GetMapping("readHZkey")
    public String readHZkey(String key){
        return deliveryService.readHZkey(key);
    }


    //批量判断某些用户今日是否还可以发送群发，采用POST方法
    @PostMapping("getUserTodayBatchSendCount")
    public EmResponse<Map<Long,Integer>> getUserTodayBatchSendCount(@RequestBody getUserTodayBatchSendCountRequest req){

        String bizKey = "BatchSend";
        LocalDate date = LocalDate.now();

        if (req.getUidSet().size()>MaxBatchCount){
            return EmResponse.of(null, 500,"批量检查用户数不能超过500个");
        }

        //调用投递行为服务，批量查询用户可否投递状态
        Map<Long, Integer> map = deliveryBehaviorService.getUserTodayBusinessCount(bizKey,req.getUidSet(),date);

        //将Map<Long,AtomicInteger>转换成返回值类型Map<Long,Integer>
        //Map<Long,Integer> backData = map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));

        return EmResponse.of(map);

    }

    /**
     * 功能描述:
     * 针对IM群组群发
     * @Param: [tcpMessageList]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2025年6月18日
     */
    @PostMapping("deliverBatchGroupTcpMessage")
    public DeliveryResponse deliverBatchGroupTcpMessage(@RequestBody List<TcpMessage> tcpMessageList){
        return (DeliveryResponse) deliveryService.deliveryBatchGroupTcpMessage(tcpMessageList);
    }


}
