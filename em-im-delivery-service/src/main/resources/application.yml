spring:
  application:
    name: im-delivery
  profiles:
    include:
      - common
      - cloud
    active: dev


#ignite:
#  igniteInstanceName: im-wsc-ignite-instance
#
#  communicationSpi:
#    localPort: 5555
#
#  discoverySpi:
#    ipFinder:
#      multicast:
#        TcpDiscoveryMulticastIpFinder:
#          multicastGroupAddress: ignite-service.test
#
#      #vmAddresses: 127.0.0.1:47500..47509
#      kubernetesIpFinder:
#        namespace: test
#        serviceName: ignite-service
#
#  dataStorageConfiguration:
#    defaultDataRegionConfiguration:
#      persistenceEnabled: false
#      initialSize: &initSize 52428800
#      maxSize: &maxSize 209715200
#
#    dataRegionConfigurations:
#      - name: data_region_wsc
#        initialSize: *initSize
#        maxSize: *maxSize
#
#
#  cacheConfiguration:
#    - name: cache-WscUserLink
#      cacheMode: PARTITIONED
#      persistenceEnabled: false
#      writeSynchronizationMode: FULL_SYNC
#      backups: 1

