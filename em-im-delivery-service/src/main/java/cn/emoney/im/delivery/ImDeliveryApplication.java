package cn.emoney.im.delivery;

import com.hazelcast.core.HazelcastInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@EnableCaching
@EnableFeignClients
@SpringBootApplication
public class ImDeliveryApplication {

    public static void main(String[] args) {

        SpringApplication.run(ImDeliveryApplication.class, args);

    }

    @Qualifier("hazelcastInstance")
    @Autowired
    private HazelcastInstance hazelcastInstance;

}
