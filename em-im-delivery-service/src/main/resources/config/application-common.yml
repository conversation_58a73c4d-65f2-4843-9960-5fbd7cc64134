spring:
  datasource:
    hikari:
      minimum-idle: 8
      maximum-pool-size: 64
      connection-test-query: SELECT 1
  data:
    redis:
      repositories:
        enabled: off
  cache:
    redis:
      key-prefix: ${spring.application.name:em-im}
  kafka:
    producer:
      retries: 0
      batch-size: 65536
      buffer-memory: 536870912 #512MB
      acks: 1
    consumer:
      group-id: ${spring.application.name:im-default}-group
      enable-auto-commit: false
      auto-offset-reset: latest
      max-poll-records: 8
    listener:
      concurrency: 4
      ack-mode: record
      missing-topics-fatal: false
server:
  http2:
    enabled: true
management:
  endpoint:
    health:
      probes:
        add-additional-paths: true
      group:
        readiness:
          include: redis
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
