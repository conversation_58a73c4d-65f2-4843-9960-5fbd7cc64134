package cn.emoney.im.api.delivery.dto;

import lombok.Getter;

/**
 * Tcp消息协议配置
 *
 * <AUTHOR>
 * {@code @date}
 **/
@Getter
public enum ProtoConstants {

    /***私聊新消息*/
    TcpMessage_Message_Private_NewCome(6101),

    /***群聊新消息 */
    TcpMessage_Message_Group_NewCome(6102),

    /***消息撤回 */
    TcpMessage_Message_Retract(6103),

    /***批量发送的私聊消息*/
    TcpMessage_Message_BatchMessage_NewCome(6104),

    /***一次性用户确认消息*/
    TcpMessage_Message_OnceUserConfirm(6105),

    /***您进入了新的私聊对话（点） */
    TcpMessage_Conversation_BeJoinNew_Private(6201),

    /***您进入了新的群聊对话（点） */
    TcpMessage_Conversation_BeJoinNew_Group(6202),

    /***群聊对话有人被管理员移出 */
    TcpMessage_Conversation_UserBeRemove(6203),

    /***群聊对话有人主动退出 */
    TcpMessage_Conversation_GroupUserQuit(6204),

    /***群聊对话被解散 */
    TcpMessage_Conversation_BeDissolve(6205),

    /***私聊对话被CMP解绑 */
    TcpMessage_Conversation_PriChatBeDissolve(6206),

    /***私聊对话关系变更（C→B1 && C→B2）*/
    TcpMessage_Conversation_RelationReplaced(6207),

    /***您有新的Agent服务连线*/
    TcpMessage_Conversation_AgentConvActived(6208),

    /***您的Agent服务连线结束了*/
    TcpMessage_Conversation_AgentConvClosed(6209),

    /***Conversation的Agent指派了坐席人员服务进行连线*/
    TcpMessage_Assign_NewPersonOnService(6210),

    /***Conversation的Agent坐席连线人员释放了*/
    TcpMessage_Assign_PersonOnAgentRelease(6211),

    /***基于Filter的Conversation_Message_NewCome虚拟会话群的新消息触达 2025年4月22日教务存在十几万班级群*/
    TcpMessage_VConversation_Message_NewCome(6221),

    /***对话状态有变化（考虑多种动因，通用）e.g:历史隐藏的群会话被@激活、或者时间出发当前历史关系对话被隐藏*/
    TcpMessage_Conversation_StatusBeChanged(6222),

    /***当前私聊对方在线状态变更 */
    TcpMessage_State_ChatPairChange(6301),

    /***当前私聊对方输入状态 */
    TcpMessage_State_ChatPairInputting(6302),

    /***您的消息阅读数据有变更 */
    TcpMessage_Read_DataChanged(6501),

    /***您所在的某个群信息有变更*/
    TcpMessage_ChatGroup_InfoChange(6601),

    /***您的联系人数据有变更*/
    TcpMessage_Contacts_Change(6701),

    /***请求远程连接*/
    TcpMessage_RemoteCtrl_QuestControl(6801),

    /***拒绝请求远程连接*/
    TcpMessage_RemoteCtrl_QuestControlRefused(6802),

    /***接受请求远程连接*/
    TcpMessage_RemoteCtrl_QuestControlAccept(6803),

    /***请求远程连接超时*/
    TcpMessage_RemoteCtrl_QuestControlTimeOut(6804),

    /***远程协助连接成功*/
    TcpMessage_RemoteCtrl_ConnectSuccess(6805),

    /***远程协助结束*/
    TcpMessage_RemoteCtrl_ConnectFinish(6806),

    /***6901 	OtpControl_MatchSuc	OTP口令匹配成功*/
    TcpMessage_OtpControl_MatchSuc(6901),

    /***6902	OtpControl_ConnectSuccess	OTP远程协助连接成功*/
    TcpMessage_OtpControl_ConnectSuccess(6902),

    /***6903	OtpControl_ConnectFinish	OTP远程协助结束*/
    TcpMessage_OtpControl_ConnectFinish(6903),


    /***远程协助端侧RCS指令*/
    TcpMessage_RemoteRC_RemoteRC_Ins(7101),

    /***7201	OtpRemoRC_Ins	OTP远程协助端侧RCS指令*/
    TcpMessage_OtpRemoRC_Ins(7201),

    /***wsc通道命令 通知并踢下线*/
    TcpMessage_WscCommand_NoticeAndKick(9101);

    private final int value;

    ProtoConstants(int value) {
        this.value = value;
    }

}

