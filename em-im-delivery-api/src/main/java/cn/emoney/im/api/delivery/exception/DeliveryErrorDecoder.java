package cn.emoney.im.api.delivery.exception;

import cn.emoney.im.common.dto.ServiceExceptionDTO;
import cn.emoney.im.common.exception.ServiceException;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Objects;

@Slf4j
public class DeliveryErrorDecoder implements ErrorDecoder {
    private final ObjectMapper objectMapper;

    public DeliveryErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Exception decode(String methodKey, Response response) {
        try (InputStream is = Objects.requireNonNull(response.body()).asInputStream()) {
            ServiceExceptionDTO dto = objectMapper.readValue(is, ServiceExceptionDTO.class);
            return DeliveryServiceException.of(dto);
        } catch (Exception e) {
            log.error(" DeliveryErrorDecoder unexpect service exception: {}", response, e);
            return new ServiceException("unexpect service exception", e);
        }
    }
}
