package cn.emoney.im.delivery.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Component
public class RedisUtils {
    private final ValueOperations<String, String> valueOps;
    private final ListOperations<String, String> listOps;
    private final ObjectMapper mapper;

    public RedisUtils(RedisTemplate<String, String> redisTemplate, ObjectMapper mapper) {
        this.valueOps = redisTemplate.opsForValue();
        this.listOps = redisTemplate.opsForList();
        this.mapper = mapper;
    }

    @SneakyThrows
    public <T> T get(String key, Class<T> clazz) {
        String result = valueOps.get(key);
        if (result == null) {
            return null;
        }
        return mapper.readValue(result, clazz);
    }

    @SneakyThrows
    public <T> List<T> getList(String key, Class<T> clazz) {
        String result = valueOps.get(key);
        if (result == null) {
            return null;
        }
        return mapper.readerForListOf(clazz).readValue(result);
    }

    @SneakyThrows
    public void set(String key, Object value) {
        String string = mapper.writeValueAsString(value);
        valueOps.set(key, string);
    }

    @SneakyThrows
    public void set(String key, Object value, Duration timeout) {
        String string = mapper.writeValueAsString(value);
        valueOps.set(key, string, timeout);
    }

    @SneakyThrows
    public Long leftPush(String key, Object value) {
        String string = mapper.writeValueAsString(value);
        return listOps.leftPush(key, string);
    }

    @SneakyThrows
    public Long rightPush(String key, Object value) {
        String string = mapper.writeValueAsString(value);
        return listOps.rightPush(key, string);
    }

    @SneakyThrows
    public <T> T leftPop(String key, Class<T> clazz) {
        String result = listOps.leftPop(key);
        if (result == null) {
            return null;
        }
        return mapper.readValue(result, clazz);
    }

    @SneakyThrows
    public <T> T rightPop(String key, Class<T> clazz) {
        String result = listOps.rightPop(key);
        if (result == null) {
            return null;
        }
        return mapper.readValue(result, clazz);
    }

    public Long listSize(String key) {
        return listOps.size(key);
    }

    public Long delete(String... key) {
        return valueOps.getOperations().delete(Arrays.asList(key));
    }

    public Boolean expire(String key, Duration timeout) {
        return valueOps.getOperations().expire(key, timeout);
    }
}
