package cn.emoney.im.api.delivery.dto;

import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.UUID;

/**
 * 通过websocket下行到用户客户端的消息类
 *
 * <AUTHOR>
 * @date 2024/01/09 10:44
 **/

@Data
public class TcpMessage {

    //ID
    private String Id;

    /**
     * 功能描述:
     * Tcp下行消息
     * @Param： mainType 消息主类型：系统消息system、业务消息business、通知消息notify
     * @Param: subType 消息子类型：聊天消息 message、对话消息 conversation、聊天群组消息 chatgroup、在线状态消息 state、联系人消息 contacts
     * @Param: messageKey 消息标识Key
     * @Param: messageBody 消息体
     * @Param: target 投递目标
     * @Param: targetType 投递目标类型 单用户、群组、全局
     * @Param: sender 发送者
     * @Return: TcpMessage
     * @Author: tengdengming
     * @Date: 2024/1/18 13:31
     */
    public TcpMessage(TcpMessageType mainType, String subType, ProtoConstants messageKey, Object messageBody, String target, TargetType targetType, String sender) {

        this.mainType = mainType;
        this.subType = subType;
        this.messageKey = messageKey;
        this.messageCode = this.messageKey.getValue();
        //this.messageBody = Base64.getEncoder().encodeToString(messageBody.getBytes(StandardCharsets.UTF_8));
        this.messageBody = messageBody;
        this.target = target;
        this.targetType = targetType;

        this.sender = sender;

        this.Id = "TM-"+UUID.randomUUID();
        this.timestamp  = Timestamp.from(Instant.now());

    }

    /**
     * 创建一个业务TcMessage消息
     *
     * @param  subType    业务消息子类型
     * @param  messageKey 消息标识Key
     * @param  messageBody 消息体
     * @param  target     消息目标
     * @param  targetType 消息目标类型
     * @param  sender      消息发送者
     * @return            a new TcpMessage
     */
    public static TcpMessage createBusinessMessage(String subType, ProtoConstants messageKey, Object messageBody, String target, TargetType targetType, String sender){
        return new TcpMessage(TcpMessageType.BUSINESS, subType, messageKey, messageBody, target, targetType,sender);
    }

    /***消息编码*/
    private Integer messageCode ;

    /***消息主类型 system,business,notify*/
    private TcpMessageType mainType;

    /***消息子类型 message,conversation,contacts*/
    private String subType;

    /***消息Key*/
    private ProtoConstants messageKey;

    /***消息体*/
    private Object messageBody;

    /***投递目标*/
    private String target;

    /***投递类型（单用户，群组）*/
    private TargetType targetType;

    /***投递者、发送者*/
    private String sender;

    /***时间戳*/
    private Timestamp timestamp;

    /***是否采用签名*/
    private Integer signRequired;

    /***是否加密*/
    private Integer encryptRequired;

    /***是否需要客户端发送TCP消息接受回执*/
    private Integer ackRequired;

    /***消息中控制提醒的用户列表，逗号分隔，可选项，格式为:uid1,uid2，默认为空。指定后对其进行mention次数的记录，避免mention过度*/
    private String mentionCtlList;

    //TODO 点对点是否需要 sender 和 target 双边通知，主要为考虑多端的情况？

}
