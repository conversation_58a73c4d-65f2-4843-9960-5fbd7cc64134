spring:


  datasource:
    # 注意 dynamic 所有配置都是独立的，不吃 Spring 本身的配置
    dynamic:
      primary: main
      hikari:
        minimum-idle: 8
        maximum-pool-size: 64
        connection-test-query: SELECT 1
      datasource:
        main:
          url: jdbc:mysql://*************:13306/emoney_im
          username: im_user
          password: im_user
        read_1:
          url: jdbc:mysql://*************:13307/emoney_im
          username: im_user
          password: im_user
        read_2:
          url: jdbc:mysql://*************:13308/emoney_im
          username: im_user
          password: im_user
        other:
          url: *******************************
          username: emim
          password: password
  redis:
    database: 6
    host: *************
  kafka:
    bootstrap-servers:
      - **************:9092
      - **************:9092
      - **************:9092
em-im:
  demo:
    online:
      timeout: 60

feign:
  client:
    config:
      wsc:
        logger-level: full
    wsc:
      url: http://testwsc.emoney.cn


logging:
  graylog:
    host: udp://test.imgraylog.emoney.cn:12201
  level:
    cn.emoney.im.delivery.manager.WebsocketManager: info
    cn.emoney.im.delivery.service.impl.DeliveryServiceImpl: info
    hazelcast: info
