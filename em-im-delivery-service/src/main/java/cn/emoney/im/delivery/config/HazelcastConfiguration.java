package cn.emoney.im.delivery.config;

import cn.emoney.im.delivery.entity.IMConn;
import com.hazelcast.config.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HazelcastConfiguration {

    @Value("${spring.profiles.active:test}")
    private String namespace;

    @Bean
    public Config hazelCastConfig() {

        Config config = new Config();

        config.getCPSubsystemConfig()
                //.setBaseDir(new File("/hazelcast"))
                .setCPMemberCount(3)
        ;

        config.getSerializationConfig().addDataSerializableFactory(IMConn.IMConnDataSerializableFactory.FACTORY_ID, new IMConn.IMConnDataSerializableFactory());

        //10.233.2.196 k8s service of hazelcast 
        config.setClusterName("im-hazelcast-cluster")
                .setInstanceName("wsc-instance")

                //configuration for map
                .addMapConfig(new MapConfig()
                        //24*60*60
                        .setName("LinkMap").setTimeToLiveSeconds(86400)
                        .setInMemoryFormat(InMemoryFormat.OBJECT)
                        .setBackupCount(1)
                       // .setEvictionConfig(new EvictionConfig().setEvictionPolicy(EvictionPolicy.LRU).setSize(1000000))
                )
                .addMapConfig(new MapConfig()
                         //两天
                        .setName("UserActivityCountMap").setTimeToLiveSeconds(172800)
                        .setMaxIdleSeconds(172800)
                         )
                //configure network
                .setNetworkConfig(new NetworkConfig()
                        .setPort(5701)
                        .setPublicAddress(System.getenv("POD_IP"))
                        .setJoin(new JoinConfig()
                                //.setMulticastConfig(new MulticastConfig().setEnabled(true))
                                .setKubernetesConfig(new KubernetesConfig()
                                        .setEnabled(true)
                                        .setProperty("namespace",namespace)
                                        .setProperty("service-name","hazelcast-service")
                                        .setProperty("service-port","5701")
                                        //.setProperty("service-dns","hazelcast-service.test")
                                        //.setProperty("pod-label","app")
                                        //.setProperty("pod-label-value","java-im-delivery")
                                )

                                .setAutoDetectionConfig(new AutoDetectionConfig()
                                        .setEnabled(true))
                        ))

                .setIntegrityCheckerConfig(new IntegrityCheckerConfig()
                        .setEnabled(true))

                .getJetConfig().setEnabled(true)
        ;

        return config;
    }


}
