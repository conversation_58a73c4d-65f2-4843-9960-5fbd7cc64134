package cn.emoney.im.delivery.entity;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.IdentifiedDataSerializable;
import lombok.Data;

import java.io.IOException;



@Data
public class IMConn implements IdentifiedDataSerializable {

    //用户ID
    private Long uid;

    //节点ID
    private Integer nodeId;

    //设备ID
    private Integer dId;

    //连接号
    private Integer fd;

    //会话
    private String session;

    //IP
    private String ip;


    public IMConn(Long uid, Integer nodeId, Integer dId, Integer fd,String session,String ip) {
        this.uid = uid;
        this.nodeId = nodeId;

        this.dId = (dId != null) ? dId : 999;
        this.fd = (fd != null) ? fd : 0;
        this.session = (session != null) ? session : "";
        this.ip = (ip != null) ? ip : "";
    }

    public IMConn() {

    }


    @Override
    public int getFactoryId() {
        return IMConnDataSerializableFactory.FACTORY_ID;
    }

    @Override
    public int getClassId() {
        return IMConnDataSerializableFactory.IM_CONN_TYPE_ID;
    }

    @Override
    public void writeData(ObjectDataOutput objectDataOutput) throws IOException {
        objectDataOutput.writeLong(uid);
        objectDataOutput.writeInt(nodeId);
        objectDataOutput.writeInt(dId);
        objectDataOutput.writeInt(fd);
        objectDataOutput.writeString(session);
        objectDataOutput.writeString(ip);

    }

    @Override
    public void readData(ObjectDataInput objectDataInput) throws IOException {
        uid = objectDataInput.readLong();
        nodeId = objectDataInput.readInt();
        dId = objectDataInput.readInt();
        fd = objectDataInput.readInt();
        session = objectDataInput.readString();
        ip = objectDataInput.readString();

    }

    //Define a factory to create instances of IMConn
    public static class IMConnDataSerializableFactory implements com.hazelcast.nio.serialization.DataSerializableFactory {
        public static final int FACTORY_ID = 1;
        public static final int IM_CONN_TYPE_ID = 1;
        public static final int IM_CONN_DEFAULT_NODE_ID = 999;

        @Override
        public IdentifiedDataSerializable create(int typeId) {
            if (typeId == IM_CONN_TYPE_ID) {
                return new IMConn();
            }
            return null;
        }


    }
}