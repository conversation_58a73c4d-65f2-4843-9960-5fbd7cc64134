<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 http://maven.apache.org/xsd/settings-1.2.0.xsd">
    <mirrors>
        <mirror>
            <id>em-public</id>
            <mirrorOf>central</mirrorOf>
            <url>https://officerepo.emoney.cn/maven-public/</url>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>em-public</id>
            <repositories>
                <repository>
                    <id>em-public</id>
                    <url>https://officerepo.emoney.cn/maven-public/</url>
                    <layout>default</layout>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>em-public</id>
                    <url>https://officerepo.emoney.cn/maven-public/</url>
                    <layout>default</layout>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>em-public</activeProfile>
    </activeProfiles>

    <servers>
        <server>
            <id>em-releases</id>
            <username>${CI_JOB_USERNAME}</username>
            <password>${CI_JOB_PASSWORD}</password>
        </server>
        <server>
            <id>em-snapshots</id>
            <username>${CI_JOB_USERNAME}</username>
            <password>${CI_JOB_PASSWORD}</password>
        </server>
    </servers>
</settings>

