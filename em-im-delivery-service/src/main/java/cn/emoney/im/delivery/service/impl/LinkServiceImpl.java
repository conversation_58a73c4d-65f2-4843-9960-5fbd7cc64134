package cn.emoney.im.delivery.service.impl;

import cn.emoney.im.api.delivery.dto.*;
import cn.emoney.im.delivery.entity.UserCmdRequest;
import cn.emoney.im.delivery.entity.IMConn;
import cn.emoney.im.delivery.service.LinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
/**
 * <AUTHOR>
 * @date 2024/04/28 14:58
 **/
public class LinkServiceImpl implements LinkService {

    private final KafkaTemplate<String,Object> kafkaTemplate;

    private final String kafkaTopic = "im-wsc-cmd-request";

    public LinkServiceImpl(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void noticeAndKick(String noticeMsg, IMConn imConn) {

        //创建wsc远程命令对象
        UserCmdRequest userCmdRequest = new UserCmdRequest();
        userCmdRequest.setGroup("EM-IM");
        userCmdRequest.setNodeId(imConn.getNodeId().toString());
        userCmdRequest.setUid(imConn.getUid().toString());
        userCmdRequest.setFd(imConn.getFd());
        userCmdRequest.setCmd("CMD-NoticeAndKick");
        userCmdRequest.setData(noticeMsg);

        TcpMessage tcpMessage = new TcpMessage(TcpMessageType.SYSTEM,
                "wsc-cmd",
                ProtoConstants.TcpMessage_WscCommand_NoticeAndKick,
                userCmdRequest,
                userCmdRequest.getUid(),
                TargetType.SINGLE_USER,
                "LinkService"
        );

        sendCmd(tcpMessage);

    }


    @Override
    public void notice(String noticeMsg, IMConn imConn) {

    }

    @Override
    public CmdResponse kick(IMConn imConn) {
        return null;
    }

    /**
     * 功能描述:
     * 向具体节点发送命令行，包括命令和数据部分
     * @Param: [nodeId, group, uid, fd, cmd, data=>userCmdRequest]
     * @Return: cn.emoney.im.api.delivery.dto.CmdResponse
     * @Author: tengdengming
     * @Date: 2024/4/28 16:29
     */
    private CmdResponse sendCmd(TcpMessage tcpMessage) {

        CmdResponse cmdResponse = new CmdResponse();
        UUID uuid = UUID.randomUUID();
        cmdResponse.setReceiptNo("CMD-"+uuid);

        try {

            //向具体节点发送命令行，包括命令和数据部分
            kafkaTemplate.send(kafkaTopic, tcpMessage);

            log.info("sendCmd:{}", tcpMessage);
        } catch (Exception e) {
            //e.printStackTrace();
            log.error("sendCmd error:{}", tcpMessage, e);
        }

        return cmdResponse;

    }


}
