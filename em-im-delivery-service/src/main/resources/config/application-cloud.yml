spring:
  cloud:
    nacos:
      server-addr: nacos.emoney.cn:80
      group: DEFAULT_GROUP
      discovery:
        namespace: ${spring.cloud.nacos.namespace:}
        group: ${spring.cloud.nacos.group:}
      config:
        namespace: ${spring.cloud.nacos.namespace:}
        group: ${spring.cloud.nacos.group:}
        file-extension: yaml
  config:
    import:
      - optional:nacos:${spring.application.name}
      - optional:nacos:im-admin-config
---
spring.config.activate.on-profile: dev
spring:
  cloud:
    nacos:
      namespace: 2e190fe6-0d79-44f0-ab51-ed55bf8b099c
---
spring.config.activate.on-profile: test
spring:
  cloud:
    nacos:
      namespace: b92ec4c0-9791-4bc3-883c-21a3877cb6cf
---
spring.config.activate.on-profile: uat
spring:
  cloud:
    nacos:
      namespace: d465d73b-dae7-401d-ac08-0f8dd690e051
---
spring.config.activate.on-profile: prod
spring:
  cloud:
    nacos:
      namespace: 8cac08b6-784d-47be-b092-b88f5591a72c
