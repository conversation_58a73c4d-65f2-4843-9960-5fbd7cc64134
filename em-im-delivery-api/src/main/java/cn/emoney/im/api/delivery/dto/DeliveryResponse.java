package cn.emoney.im.api.delivery.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Time;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 消息投递返回类
 *
 * <AUTHOR>
 * @date 2024/01/10 09:41
 **/
@Data
public class DeliveryResponse {

    public DeliveryResponse() {
        this.requestTime = LocalDateTime.now();
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime requestTime;

    public String receiptNo;

    public String deliveryDetail;

    public Integer code;

}
