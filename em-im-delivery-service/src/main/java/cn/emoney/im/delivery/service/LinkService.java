package cn.emoney.im.delivery.service;

import cn.emoney.im.api.delivery.dto.CmdResponse;
import cn.emoney.im.delivery.entity.IMConn;

public interface LinkService {


    /**
     * notice and kick
     *
     * @param noticeMsg description of parameter
     * @param imConn    description of parameter
     * @return description of return value
     */
    void noticeAndKick(String noticeMsg, IMConn imConn);

    /**
     * send notice
     *
     * @param noticeMsg description of parameter
     * @param imConn    description of parameter
     */
    void notice(String noticeMsg, IMConn imConn);


    /**
     * kick off the imConn
     *
     * @param imConn description of parameter
     * @return description of return value
     */
    CmdResponse kick(IMConn imConn);


}
