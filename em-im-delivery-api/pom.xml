<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.emoney.im</groupId>
        <artifactId>em-im-delivery</artifactId>
        <version>2.1.1-SNAPSHOT</version>
    </parent>

    <artifactId>em-im-delivery-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>cn.emoney.im</groupId>
            <artifactId>em-im-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.1</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.emoney.im</groupId>
            <artifactId>em-im-starter-api</artifactId>
            <optional>true</optional>
        </dependency>



    </dependencies>

</project>