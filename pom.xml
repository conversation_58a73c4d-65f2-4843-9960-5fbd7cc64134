<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.emoney.im.</groupId>
        <artifactId>em-im-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <artifactId>em-im-delivery</artifactId>
    <version>2.1.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>em-im-delivery</name>
    <description>em-im-delivery</description>

    <properties>
        <java.version>21</java.version>
    </properties>

    <modules>
        <module>em-im-delivery-api</module>
        <module>em-im-delivery-service</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>em-releases</id>
            <url>https://officerepo.emoney.cn/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>em-snapshots</id>
            <url>https://officerepo.emoney.cn/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
