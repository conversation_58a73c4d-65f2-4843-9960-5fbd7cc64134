package cn.emoney.im.delivery.service.impl;

import cn.emoney.im.api.chatgroup.dto.ChatGroupMemberModel;
import cn.emoney.im.api.chatgroup.service.ChatGroupApi;
import cn.emoney.im.api.delivery.dto.DeliveryResponse;
import cn.emoney.im.api.delivery.dto.ProtoConstants;
import cn.emoney.im.api.delivery.dto.TargetType;
import cn.emoney.im.api.delivery.dto.TcpMessage;
import cn.emoney.im.delivery.entity.DeliveryRequest;
import cn.emoney.im.delivery.entity.IMConn;
import cn.emoney.im.delivery.manager.WebsocketManager;
import cn.emoney.im.delivery.service.DeliveryBehaviorService;
import cn.emoney.im.delivery.service.DeliveryService;
import cn.emoney.im.delivery.service.LinkService;
import cn.emoney.im.delivery.utils.RedisUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.FutureTask;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.concurrent.Executors.*;

/**
 * 功能描述:
 * TCP消息投递服务实现类
 * @Param:
 * @Return:
 * @Author: tengdengming
 * @Date: 2024/2/23 10:30
 */

@Slf4j
@Service
public class DeliveryServiceImpl implements DeliveryService {


    @Value("${spring.profiles.active:test}")
    private String envName;

    private final String TcpGroup = "EM-IM";
    private  final ChatGroupApi chatGroupApiService;

    private final WebsocketManager websocketManager;

    private final RedisUtils redisUtils;

    private final int DURATION_ChatGroupList = 60*5;

    private final int DURATION_Progress = 60*24;

    private final String REDISKEY_Delivery_progress ="Delivery:asyncJob:progress:";

    private final String REDISKEY_ChatGroupList = "Delivery:chatGroupList:";

    private final String hazelMapLinkMap = "LinkMap";

    private final String hazelcastMultiMapName="LinkMultiMap";

    private final ObjectMapper mapper;

    //private final Ignite ignite;

    private final HazelcastInstance hazelcastInstance;

    private final DeliveryBehaviorService deliveryBehaviorService;

    private final LinkService linkService;

    private final KafkaTemplate<String,Object> kafkaTemplate;

    //private final String kafkaTopic = "im-delivery-wsc-request-"+envName;
    private final String kafkaTopic = "im-delivery-wsc-request";

    //private static final Logger logger = org.slf4j.LoggerFactory.getLogger(DeliveryServiceImpl.class);

    //创建一个执行器
    private final ExecutorService executor = newVirtualThreadPerTaskExecutor();


    public DeliveryServiceImpl(ChatGroupApi chatGroupApiService, WebsocketManager websocketManager, RedisUtils redisUtils, ObjectMapper mapper, @Qualifier("hazelcastInstance") HazelcastInstance hazelcastInstance, DeliveryBehaviorService deliveryBehaviorService, LinkService linkService, KafkaTemplate<String, Object> kafkaTemplate) {
        this.chatGroupApiService = chatGroupApiService;
        this.websocketManager = websocketManager;
        this.redisUtils = redisUtils;
        this.mapper = mapper;
        //this.ignite = ignite;

        this.hazelcastInstance = hazelcastInstance;
        this.deliveryBehaviorService = deliveryBehaviorService;
        this.linkService = linkService;
        this.kafkaTemplate = kafkaTemplate;
    }

    /***
     * 功能描述:
     * 投递TcpMessage消息
     * @Param: [tcpMessage]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/1/11 14:43
     */
    @Override
    public DeliveryResponse deliveryTcpMessage(TcpMessage tcpMessage) {

        return switch (tcpMessage.getTargetType()) {
            case GLOBAL ->
                // 处理全局消息
                    deliveryGlobalTcpMessage(tcpMessage);
            case SINGLE_USER ->
                // 处理单用户消息
                    deliverySingleUserTcpMessage(tcpMessage);
            case GROUP ->
                // 处理群组消息
                    deliveryGroupTcpMessage(tcpMessage);
            case BATCH_USER ->
                // 处理批量用户消息，如果是用逗号分隔的多个单用户
                    deliveryMultiUserTcpMessage(tcpMessage);
            default ->
                // 处理其他情况（如果有的话）
                new DeliveryResponse();
        };

    }



    @Override
    public List<DeliveryResponse> deliveryTcpMessageList(List<TcpMessage> tcpMessageList) {

        List<DeliveryResponse> retList = new ArrayList<>();

        for (TcpMessage tcpMessage : tcpMessageList) {
            retList.add(deliveryTcpMessage(tcpMessage));
        }

        return retList;
    }

    /**
     * 功能描述:
     * 获取对应投递标识的处理状态
     * @Param: [receiptNo]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/1/15 18:09
     */
    @Override
    public String getDeliveryStatus(String receiptNo) {
        return getJobProgress(receiptNo);
    }



    /**
     * 功能描述:
     * 客户端连接成功后上报连接信息
     * @Param: [receiptNo, nodeId]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/2/21 09:28
     */
    @Override
    public String connectWscDone(Long uid, Integer nodeId, Integer dId, Integer fd, String session,String ip){

        IMap<Long, List<IMConn>> map = hazelcastInstance.getMap(hazelMapLinkMap);
        List<IMConn> userCurrentList = map.computeIfAbsent(uid, k -> new ArrayList<>());

        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }

        IMConn newComingConn = new IMConn(uid,nodeId,dId,fd,session,ip);

        if (userCurrentList.stream().noneMatch(imConn -> imConn.getDId().equals(dId))){
            //新的DId类型，之前不存在这种did
            userCurrentList.add(newComingConn);
        }else{
            //之前存在did
            Optional<IMConn> optionalCur = userCurrentList.stream()
                    .filter(imConn -> imConn.getDId().equals(dId) && imConn.getSession().equals(session))
                    .findFirst();

            if (optionalCur.isPresent()) {
                //相同的session，说明同一个页面重连，则更新fd
                optionalCur.get().setFd(fd);

            } else {
                //找到之前did+session的IMConn
                IMConn oldIMConn = userCurrentList.stream()
                        .filter(imConn -> imConn.getDId().equals(dId))
                        .findFirst()
                        .orElse(null);

                if (oldIMConn != null){
                    log.debug("connectWscDone: find conflict IMConn.dId:{} of userId:{}, oldIMConn:{}", dId, uid, oldIMConn);
                    //kick之前的IMConn
                    linkService.noticeAndKick("connection reset by another page(new session="+session+" )", oldIMConn);

                    userCurrentList.remove(oldIMConn);
                }

                //添加新的IMConn
                userCurrentList.add(newComingConn);
            }

        }

        map.put(uid, userCurrentList);

        log.debug("connectWscDone:update userCurrentList over! now => {}", userCurrentList);

        //将userCurrentList列表用逗号分割连接字符串
        return userCurrentList.stream().map(IMConn::getNodeId).map(Object::toString).collect(Collectors.joining(","));
    }


    /***
     * 功能描述:
     * 从LinkMap中移除这个IMConn
     * @Param: [imConn]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2024/5/20 0020 10:04:07
     */
    @Override
    public void closeWscDone(IMConn imConn) {

        IMap<Long, List<IMConn>> map = hazelcastInstance.getMap(hazelMapLinkMap);
        List<IMConn> userCurrentList = map.computeIfAbsent(imConn.getUid(), k -> new ArrayList<>());

        //移除之前的IMConn
        userCurrentList.removeIf(ic -> imConn.getDId().equals(ic.getDId()) && imConn.getSession().equals(ic.getSession()));
    }


    //判断IMConn是否构成页面级会话冲突
    private Boolean isSessionConflict(IMConn newCome,List<IMConn> currentList){
        //判断是否存在相同的did和session的IMConn，如果存在则将fd更新为最新的，并且将原来的IMConn信息发送到连接服务进行Kick
        return currentList.stream().anyMatch(imConn -> imConn.getDId().equals(newCome.getDId()) && imConn.getSession().equals(newCome.getSession()));
    }


    /***
     * 功能描述:
     * 获取用户连接上报的NodeId
     * @Param: [uid]
     * @Return: java.lang.Integer
     * @Author: tengdengming
     * @Date: 2024/3/11 13:10
     */
    @Override
    public List<IMConn> getUserWscConnections(Long uid) {

        //压测停止精准路由逻辑
        //return new ArrayList<>();

        IMap<Long, List<IMConn>> map = hazelcastInstance.getMap(hazelMapLinkMap);
        return map.get(uid);
    }

    /***
     * 功能描述:
     * 批量获取用户连接上报的NodeId
     * @Param: [uidSet]
     * @Return: java.util.Map<java.lang.Long,java.lang.Integer>
     * @Author: tengdengming
     * @Date: 2024/3/11 13:10
     */
    @Override
    public Map<Long, List<IMConn>> getUserWscNodeIds(Set<Long> uidSet) {
        //压测停止精准路由逻辑
        return new HashMap<>();
        /*
        IMap<Long, List<IMConn>> map = hazelcastInstance.getMap(hazelcastMapName);
        return map.getAll(uidSet);*/
    }


    /***
     * 功能描述:
     *  写入缓存
     * @Param: [key, value]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/3/12 09:23
     */
    @Override
    public String writeHZkey(String key, String value) {
        IMap<String, String> map = hazelcastInstance.getMap(hazelMapLinkMap);
        map.put(key, value);
        return "success:key:"+key+" value:"+value;
    }

    /***
     * 功能描述:
     *   读取缓存
     * @Param: [key]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/3/12 09:23
     */

    @Override
    public String readHZkey(String key) {
        IMap<String, String> map = hazelcastInstance.getMap(hazelMapLinkMap);

        if (key.contains(",")){
            String[] arr = key.split(",");
            HashSet<String> set = new HashSet<>(Arrays.asList(arr));
            StringBuilder sb = new StringBuilder();

            map.getAll(set).forEach((k, v) -> {
                sb.append(k).append(":").append(v).append(",");
            });

            return sb.toString();
        }

        return map.get(key);

    }


    /***
     * 功能描述:
     * 投递单个用户的TCPMessage消息
     * @Param: []
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/1/11 14:40
     */
    private  DeliveryResponse deliverySingleUserTcpMessage(TcpMessage tcpMessage){

        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("SRT-"+uuid);
        dr.setDeliveryDetail(tcpMessage.getId());

        try {

            /*List<IMConn> userIMConn = getUserWscConnections(Long.valueOf(tcpMessage.getTarget()));

            log.info("获取到用户{}的IMConn:{},准备投递单个用户的TCPMessage:{}",tcpMessage.getTarget(),userIMConn,tcpMessage);

            if (userIMConn!=null && !userIMConn.isEmpty()){
                *//*
                //判断是否有节点信息，没有则默认发送
                //userIMConn列表根据nodeId值进行去重
                List<IMConn> uniqueIMConn = userIMConn.stream().filter(imConn -> imConn.getNodeId()!=null).distinct().toList();

                 if (uniqueIMConn!=null && !uniqueIMConn.isEmpty()){

                    log.debug("获取到用户{}的nodeId:{}，采用sendMessageWithNodeId",tcpMessage.getTarget(),userIMConn);
                    uniqueIMConn.forEach(imConn -> {
                         String postBack = sendMessageWithNodeId(tcpMessage,imConn.getNodeId());
                         if (!"done".equals(postBack)){
                             log.error("IMConn多个连接循环投递单个用户的TCPMessage消息异常：{}",postBack);
                         }
                    });

                }else{
                    log.debug("未获取到用户{}的nodeId，采用postMessage",tcpMessage.getTarget());
                    postBackStr = postMessage(tcpMessage);
                }*//*
            }*/

            //异步更新投递的状态数据
            CompletableFuture.runAsync(()->{
                String postBackStr = "";

                //集群订阅模式
                postBackStr = postMessage(tcpMessage);

                //if("done".equals(postBackStr)){
                updateJobProgress(dr.getReceiptNo(),1,1);
                //}

            },executor);

        } catch (Exception e) {
            log.error("投递单个用户的TCPMessage消息异常:{}",e.getMessage());
        } finally {
            //UdpLog记录投递的Dr
            log.info("deliverySingleUserTcpMessage投递DeliveryResponse:{}",dr);
        }

        return dr;

    }

    /***
     * 功能描述:
     * 一次性投递一条消息给多个用户（N合1）
     * @Param: [tcpMessage]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/3/28 15:14
     */
    private DeliveryResponse deliveryMultiUserTcpMessage(TcpMessage tcpMessage){

        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("MRT-"+uuid);
        dr.setDeliveryDetail(tcpMessage.getId());

        try {
            int supportLength = 1000;
            //判断是否超过支持长度
            String[] originArr = tcpMessage.getTarget().split(",");

            if(originArr.length>supportLength) {
                //originArr限制到1000，转换成逗号分隔连接的字符串
                tcpMessage.setTarget(Arrays.toString(Arrays.copyOf(originArr, supportLength)).replace("[","").replace("]","").replace(" ",""));
                log.error("投递多合一用户的TCPMessage消息超过1000人，截取1000人发送=>{}",tcpMessage.getTarget());
            }

            //采用异步投递
            CompletableFuture.runAsync(
                    () -> {
                        postMessage(tcpMessage);
                        updateJobProgress(dr.getReceiptNo(),1,1);
                    },executor
            );

        } catch (Exception e) {
            log.error("投递多合一用户的TCPMessage消息异常:{}",e.getMessage());
        }finally {
            log.info("deliveryMultiUserTcpMessage投递DeliveryResponse:{}",dr);
        }

        return dr;
    }


    /***
     * 功能描述:
     * 针对群组进行Tcp消息的投递
     * @Param: []
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/1/11 14:40
     */
    private DeliveryResponse deliveryGroupTcpMessage(TcpMessage tcpMessage){

        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("GRT-"+uuid);
        dr.setDeliveryDetail(tcpMessage.getId());

        try {
            //获取需要推送的群组成员列表
            long chatGroupId = Integer.parseInt(tcpMessage.getTarget());
            List<ChatGroupMemberModel> chatGroupUserList =  chatGroupApiService.getRealMembersByChatGroupId(chatGroupId);

            // 检查列表是否为空
            if (!chatGroupUserList.isEmpty()) {

                // 获取群组成员的uid集合，用逗号连接
                String userIds= chatGroupUserList.stream().map(ChatGroupMemberModel::getUserId).map(String::valueOf).collect(Collectors.joining(","));

                //采用异步投递
                CompletableFuture.runAsync(
                        () -> {
                            tcpMessage.setTarget(userIds);
                            postMessage(tcpMessage);

                            updateJobProgress(dr.getReceiptNo(),1,1);
                        },executor
                );

            }

            //todo 节点路由投递逻辑（待确认）
            /*// 检查列表是否为空
            if (!chatGroupUserList.isEmpty()) {

                // 获取群组成员的uid集合
                Set<Long> chatGroupUserUidSet = chatGroupUserList.stream().map(ChatGroupMemberModel::getUserId).collect(Collectors.toSet());
                log.debug(" AtomicInt i={} 获取群组成员的用户ID集合:chatGroupUserUIdSet {}",i.get(),chatGroupUserUidSet);

                //获取当前标记了wsc节点信息的用户
                Map<Long, List<IMConn>> userNodeIdSet = getUserWscNodeIds(chatGroupUserUidSet);
                //log.debug("获取当前标记了wsc节点信息的用户:userNodeIdSet {}",userNodeIdSet);

                //按节点打包用户
                Map<Integer, Set<Long>> packageMap = packageDeliveryUserGroupByNodeId(userNodeIdSet,chatGroupUserUidSet);
                //log.debug("按节点打包用户:packageMap {}",packageMap);

                //todo 是否存在当绝大多数没有节点路由形成节点批任务的，可以采用多播投递，当达到一定比例则使用节点批投递，再附加多播投递。

                int total = packageMap.size();
                List<CompletableFuture<String>> futures = new ArrayList<>();

                //log.debug("packageMap.size={}",total);

                // 获取MultiMap中的键（nodeId）,循环节点进行批量发送
                for (Integer k : packageMap.keySet()) {

                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {

                        // 将value部分的List 连接为逗号分割的字符串
                        String userIds = packageMap.get(k).stream().map(String::valueOf).collect(Collectors.joining(","));
                        log.debug("package this node delivery requirement => nodeId={},UserIds={}",k,userIds);

                        tcpMessage.setTarget(userIds);

                        sendMessageWithNodeId(tcpMessage,  k);
                        return userIds;

                    }).thenApply(nodeIds -> {
                        int currentProgress = i.getAndIncrement();
                        updateJobProgress(dr.getReceiptNo(), currentProgress, total);
                        return nodeIds;
                    });
                    futures.add(future);
                }

                // 等待所有异步任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                // 组装最终返回的字符串
                CompletableFuture<String> result = allFutures.thenApply(v -> {
                    return futures.stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.joining(","));
                });

                // 获取最终结果
                String finalResfinalResultult = result.join();

            }*/


        }
        catch (Exception e) {
           log.error("投递群组TCPMessage消息异常:{}",tcpMessage,e);
        }finally {
            log.info("deliveryGroupTcpMessage投递DeliveryResponse:{}",dr);
        }

        return  dr;
    }


    /**
     * 功能描述:
     * 按照集群的节点维度来打包用户（投递一次，投递次数降低到节点规模）
     * @Param: [currentOnlineConnMap, chatGroupUserUidSet]
     * @Return: java.util.Map<java.lang.Integer,java.util.Set<java.lang.Long>>
     * @Author: tengdengming
     * @Date: 2024/3/15 16:01
     */
    private Map<Integer,Set<Long>> packageDeliveryUserGroupByNodeId(Map<Long, List<IMConn>> currentOnlineConnMap,Set<Long> chatGroupUserUidSet){

        // 创建一个MultiMap,Map<nodeId,userId>
        Map<Integer, Set<Long>> packagedMap = new HashMap<>();

        //针对每个用户，根据nodeId进行分组
        chatGroupUserUidSet.forEach(uid ->{

            //如果未查到标记路由，则默认走999多播投递
            if(!currentOnlineConnMap.containsKey(uid)){
                packagedMap.computeIfAbsent(999,x->new HashSet<>()).add(uid);
            }else{
                //获取用户标记wsc的节点信息
                currentOnlineConnMap.get(uid).forEach(imConn -> {
                    //todo 可以按需要进行多端检查
                    packagedMap.computeIfAbsent(imConn.getNodeId(), x -> new HashSet<>()).add(uid);
                });
            }

        });

        return packagedMap;
    }


    /**
     * 功能描述:
     * 发送Global全局消息，限于wsc的Group范围内
     * @Param: []
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/1/11 14:40
     */
    private  DeliveryResponse deliveryGlobalTcpMessage(TcpMessage tcpMessage){

        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("RT-"+uuid);

        //tcpMessage.setAckRequired(1);
        dr.setDeliveryDetail(tcpMessage.getId());
        tcpMessage.setTarget("");//全局消息设置为""

        try {

            //异步更新投递的状态数据
            CompletableFuture.runAsync(()->{
                String postBackStr = "";

                //集群订阅模式
                postBackStr = postMessage(tcpMessage);

                //if("done".equals(postBackStr)){
                updateJobProgress(dr.getReceiptNo(),1,1);
                //}

            },executor);

        } catch (Exception e) {
            log.error("投递全局用户的TCPMessage消息异常:{}",e.getMessage());
        } finally {
            //UdpLog记录投递的Dr
            log.info("deliverySingleUserTcpMessage投递DeliveryResponse:{}",dr);
        }

        return dr;
    }



    /***
     * 功能描述:
     * 单聊群发工具发送——群发功能
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2024/2/22 14:13
     * 2024年12月9日
     * 进行重构，支持是否记录消息，抽取出 private 方法 deliveryBatchMessages
     */
    @Override
    public DeliveryResponse deliveryBatchUserTcpMessage(List<TcpMessage> tcpMessageList) {

        return deliveryBatchMessages(tcpMessageList, true);

    }

    @Override
    public DeliveryResponse deliveryBatchUserTcpMessageIgnoreCount(List<TcpMessage> tcpMessageList) {
           // Sending messages without counting
            return deliveryBatchMessages(tcpMessageList, false);

    }


    @Override
    public DeliveryResponse deliverySingleUserTcpMessage(List<TcpMessage> tcpMessageList) {
        //TODO
        return null;
    }


    /***
     * 功能描述:
     * 批量群发
     * 2024年12月9日 抽取，支持忽略计数（图文+附件）
     * @Param: [tcpMessageList, countMessages]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2024/12/9 0009 11:42:00
     */
    private DeliveryResponse deliveryBatchMessages(List<TcpMessage> tcpMessageList, boolean countMessages) {

        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("BRT-" + uuid);
        dr.setDeliveryDetail("start batch send messages, total:" + tcpMessageList.size());

        AtomicInteger i = new AtomicInteger();

        // Checking if any TcpMessage has incorrect TargetType
        for (TcpMessage tcpMessage : tcpMessageList) {
            if (tcpMessage.getTargetType() != TargetType.BATCH_USER) {
                dr.setDeliveryDetail("current TcpMessage.TargetType is not 'BATCH_USER'");
                return dr;
            }
        }

        // Proceeding with sending messages
        if (!tcpMessageList.isEmpty()) {
            int total = tcpMessageList.size();
            dr.setDeliveryDetail("start batch send messages, total:" + total);

            try {
                List<CompletableFuture<String>> futures = tcpMessageList.stream()
                        .map(member -> CompletableFuture.supplyAsync(() -> {

                            postMessage(member);

                            if (countMessages) {
                                i.getAndIncrement();
                                updateJobProgress(dr.getReceiptNo(), i.get(), total);
                            }

                            return "async done";
                        }, executor))
                        .toList();
            } catch (Exception ex) {
                log.error("deliverBatchUserTcpMessage(): send message error", ex);
            } finally {

                if (countMessages){
                    log.info("finally 异步调用记录用户的群发次数，List{}",tcpMessageList);

                    //异步调用记录用户的群发次数，目前单日限制5次
                    FutureTask<String> futureTask = new FutureTask<>(() -> {

                        //获取tcpMessageList中的UId，转换成Set<Long>
                        Set<Long> uidSet = tcpMessageList.stream()
                                .map(tcpMessage -> Long.valueOf(tcpMessage.getTarget()))
                                .collect(Collectors.toSet());

                        try {
                            //记录单聊群发的用户活动次数
                            deliveryBehaviorService.setUserTodayBusinessCount("BatchSend",uidSet, LocalDate.now());

                            //记录单聊群发的用户活动次数
                            log.debug("BatchSend uidSet:"+uidSet.size());

                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                        return "done";
                    });

                    executor.submit(futureTask);

                    log.info("deliveryBatchUserTcpMessage投递DeliveryResponse:{}",dr);
                }

            }
        }

        return dr;
    }



    /**
     * 给单个用户发送TcpMessage
     * @Param: [member, tcpMessage]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/1/12 16:08
     */
    private  String postMessage(TcpMessage tcpMessage)  {
        DeliveryRequest reqBody;

        try {

            String target = tcpMessage.getTarget();
            //如果投递目标长度为批量大用户群体，则替换为空，但是要考虑引用剔除后，下游对其的使用

            if (target.length()>100) {
                tcpMessage.setTarget("");
            }

            //排除NPC
            /*if(Integer.parseInt(tcpMessage.getSender()) > 10000){
                //todo 在目前没有多端同步业务需求的情况下，暂时直接在业务中处理6101和6103多端同步逻辑（非标准化实现）
                if(tcpMessage.getMessageKey() == ProtoConstants.TcpMessage_Message_Private_NewCome || tcpMessage.getMessageKey()== ProtoConstants.TcpMessage_Message_Retract) {

                    //检测发件人是否存在多端在线
                    List<IMConn> senderConnCount = getUserWscConnections(Long.valueOf(tcpMessage.getSender()));

                    if (senderConnCount.size()>1) {
                        //启动发件人多端同步逻辑
                        target += ","+tcpMessage.getSender();
                        //记录日志
                        log.info("sender:{},senderConnCount:{},target:{}",tcpMessage.getSender(),senderConnCount.size(),target);
                    }
                }
            }*/

            reqBody = new DeliveryRequest(target,mapper.writeValueAsString(tcpMessage),TcpGroup);

        } catch (Exception e) {
            log.error("postMessage() error : ", e);
            throw new RuntimeException(e);
        }

        //推送到kafka
        kafkaTemplate.send(kafkaTopic,reqBody);
        log.info("postMessage() call kafkaTemplate.send kafkaTopic: {} reqBody: {}",kafkaTopic,reqBody);

        /*ResponseEntity<String>  responseEntity = websocketManager.pushMessage(reqBody);

        String backBody = responseEntity.getBody();
        log.debug("postMessage(): backBody:{}",backBody);*/

        return "done";
    }


    /***
     * 指定nodeId对单个用户进行消息发送
     * @Param: [tcpMessage, nodeId]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/3/7 19:33
     */
    private String sendMessageWithNodeId(TcpMessage tcpMessage,Integer nodeId)  {
        DeliveryRequest reqBody;

        log.debug("DeliveryServiceImpl.sendMessageWithNodeId(): tcpMessage:{},nodeId:{}",tcpMessage,nodeId);

        try {
            String target = tcpMessage.getTarget();
            tcpMessage.setTarget("");

            reqBody = new DeliveryRequest(target,mapper.writeValueAsString(tcpMessage),TcpGroup);

            //推送到kafka
            kafkaTemplate.send(kafkaTopic,reqBody);

            /*ResponseEntity<String>  responseEntity = websocketManager.sendMessage2Node(reqBody,nodeId);
            String backBody = responseEntity.getBody();
            log.debug("sendMessage2Node(tcpMessage,{})-> HTTPBackBody:{}",nodeId,backBody);*/

        } catch (JsonProcessingException e) {
            log.error("sendMessage2Node(): JsonProcessingException:{}",e.getMessage());
            throw new RuntimeException(e);
        }

        return "done";

    }


    /**
     * 功能描述:
     * 更新投递任务的进度
     * @Param: [currentIndex, total]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2024/1/15 14:03
     */
    private void updateJobProgress(String receiptNo,int currentIndex,int total){
        //redisUtils.set(REDISKEY_Delivery_progress+receiptNo,currentIndex+"/"+total, Duration.ofMinutes(DURATION_Progress));
    }


    /**
     * 功能描述:
     * 获取投递任务的进度
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2024/1/15 14:05
     */
    private String getJobProgress(String receiptNo){
        return redisUtils.get(REDISKEY_Delivery_progress+receiptNo,String.class);

    }

    /**
     * 功能描述:
     * 获取群组的成员列表数据
     * @Param: [chatGroupId]
     * @Return: java.util.List<cn.emoney.im.api.chatgroup.dto.ChatGroupMemberModel>
     * @Author: tengdengming
     * @Date: 2024/1/15 18:48
     */
    private List<ChatGroupMemberModel> getChatGroupMemberList(long chatGroupId){

        List<ChatGroupMemberModel> cacheMemberList = redisUtils.getList(REDISKEY_ChatGroupList,ChatGroupMemberModel.class);

        if (cacheMemberList!=null){
            return cacheMemberList;
        }else{
            List<ChatGroupMemberModel> chatGroupUserList =  chatGroupApiService.getRealMembersByChatGroupId(chatGroupId);

            redisUtils.set(REDISKEY_ChatGroupList+chatGroupId,chatGroupUserList,Duration.ofMinutes(DURATION_ChatGroupList));

            return chatGroupUserList;
        }

    }

    /***
     * 功能描述:
     * 获取本地的某ChatGroup的Hash值
     * @Param: [chatGroupId]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/1/15 18:59
     */
    private String getLocalChatGroupListHash(long chatGroupId){
        return redisUtils.get(REDISKEY_ChatGroupList+":hash:"+chatGroupId,String.class);
    }

    /**
     * 功能描述:
     * 设置本地的某ChatGroup的Hash值
     * @Param: [chatGroupId, remoteHash]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2024/1/15 19:08
     */
    private void setLocalChatGroupListHash(long chatGroupId,String remoteHash){
        redisUtils.set(REDISKEY_ChatGroupList+":hash:"+chatGroupId,remoteHash,Duration.ofMinutes(DURATION_ChatGroupList));
    }

    /***
     * 功能描述:
     * 获取远端ChatGroup的hash值
     * @Param: [chatGroupId]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2024/1/15 18:59
     */
    private String getRemoteChatGroupListHash(long chatGroupId){

        //Todo chatGroupApiService 需要实现一个基于Hash的版本检查，不变则不获取详情
        return "";

    }


    //2025年6月18日 增加群发助手中针对群进行群发的TCP消息推送
    @Override
    public DeliveryResponse deliveryBatchGroupTcpMessage(List<TcpMessage> tcpMessageList) {
        return deliveryBatchGroupMessages(tcpMessageList, true);
    }

    @Override
    public DeliveryResponse deliveryBatchGroupTcpMessageIgnoreCount(List<TcpMessage> tcpMessageList) {
        return deliveryBatchGroupMessages(tcpMessageList, false);
    }

    /***
     * 功能描述:
     * 批量群发群组消息
     * @Param: [tcpMessageList, countMessages]
     * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
     * @Author: tengdengming
     * @Date: 2025/06/18 11:42:00
     */
    private DeliveryResponse deliveryBatchGroupMessages(List<TcpMessage> tcpMessageList, boolean countMessages) {
        DeliveryResponse dr = new DeliveryResponse();
        UUID uuid = UUID.randomUUID();
        dr.setReceiptNo("BRT-" + uuid);
        dr.setDeliveryDetail("start batch send group messages, total:" + tcpMessageList.size());

        //当Size大于100时，拒绝进行发送
        if (tcpMessageList.size() > 100) {
            dr.setDeliveryDetail("too many messages, reject to send");
            return dr;
        }

        AtomicInteger i = new AtomicInteger();

        // Checking if any TcpMessage has incorrect TargetType
        for (TcpMessage tcpMessage : tcpMessageList) {
            if (tcpMessage.getTargetType() != TargetType.BATCH_GROUP) {
                dr.setDeliveryDetail("current TcpMessage.TargetType is not 'BATCH_GROUP'");
                return dr;
            }
        }

        // Proceeding with sending messages
        if (!tcpMessageList.isEmpty()) {
            int total = tcpMessageList.size();
            dr.setDeliveryDetail("start batch send group messages, total:" + total);

            try {
                List<CompletableFuture<String>> futures = tcpMessageList.stream()
                        .map(member -> CompletableFuture.supplyAsync(() -> {
                            deliveryGroupTcpMessage(member);

                            if (countMessages) {
                                i.getAndIncrement();
                                updateJobProgress(dr.getReceiptNo(), i.get(), total);
                            }

                            return "async done";
                        }, executor))
                        .toList();

            } catch (Exception ex) {
                log.error("deliveryBatchGroupMessages(): send message error", ex);
            } finally {

                if (countMessages) {
                    log.info("finally 异步调用记录群组群发中每个mentionCtlList中用户被群发的次数，List{}", tcpMessageList);

                    //异步调用记录TcpMessage中的mentionCtlList涉及的用户的被群发次数。
                    FutureTask<String> futureTask = new FutureTask<>(() -> {

                        //获取tcpMessageList中单个Message的mentionCtlList(,逗号分割的uid)，拼接成一个UIdSet
                        Set<Long> uidSet = tcpMessageList.stream()
                                .flatMap(tcpMessage -> Arrays.stream(tcpMessage.getMentionCtlList().split(",")))
                                .map(Long::valueOf)
                                .collect(Collectors.toSet());

                        try {
                            //记录群组批量群发中的mentionCtlList涉及的用户的当天提醒mention的活动次数，间接可以通过客户端进行发送的时候拒绝
                            deliveryBehaviorService.setUserTodayBusinessCount("BatchSend", uidSet, LocalDate.now());

                            //记录群组群发的活动次数
                            log.debug("BatchSendGroupMessage→groupIdSet:" + uidSet.size());

                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }

                        return "done";
                    });

                    executor.submit(futureTask);

                    log.info("deliveryBatchGroupMessages投递DeliveryResponse:{}", dr);
                }
            }
        }

        return dr;
    }


}
