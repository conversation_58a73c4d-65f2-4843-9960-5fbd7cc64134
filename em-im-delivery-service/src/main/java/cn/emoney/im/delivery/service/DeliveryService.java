package cn.emoney.im.delivery.service;


import cn.emoney.im.api.delivery.dto.DeliveryResponse;
import cn.emoney.im.api.delivery.dto.TcpMessage;
import cn.emoney.im.delivery.entity.IMConn;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Tcp消息投递服务接口类
 *
 * <AUTHOR>
 * @date 2024/01/08 11:45
 **/
public interface DeliveryService  {

DeliveryResponse deliveryTcpMessage(TcpMessage tcpMessage);

List<DeliveryResponse> deliveryTcpMessageList(List<TcpMessage> tcpMessageList);

DeliveryResponse deliveryBatchUserTcpMessage(List<TcpMessage> tcpMessageList);

DeliveryResponse deliveryBatchUserTcpMessageIgnoreCount(List<TcpMessage> tcpMessageList);

DeliveryResponse deliverySingleUserTcpMessage(List<TcpMessage> tcpMessageList);

/**
 * 功能描述:
 * 群聊群发工具发送——群发功能
 * @Param: tcpMessageList
 * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
 * @Author: tengdengming
 * @Date: 2024/2/22 14:13
 * 2024年12月9日
 * 进行重构，支持是否记录消息，抽取出 private 方法 deliveryBatchMessages
 */
DeliveryResponse deliveryBatchGroupTcpMessage(List<TcpMessage> tcpMessageList);


/**
 * 功能描述:
 * 群聊群发工具发送——群发功能（忽略计数）
 * @Param: tcpMessageList
 * @Return: cn.emoney.im.api.delivery.dto.DeliveryResponse
 * @Author: tengdengming
 * @Date: 2024/2/22 14:13
 * 2024年12月9日
 * 进行重构，支持是否记录消息，抽取出 private 方法 deliveryBatchMessages
 */
DeliveryResponse deliveryBatchGroupTcpMessageIgnoreCount(List<TcpMessage> tcpMessageList);

String getDeliveryStatus(String receiptNo);

String connectWscDone(Long uid, Integer nodeId,Integer dId,Integer fd,String session,String ip);

void closeWscDone(IMConn imConn);

List<IMConn> getUserWscConnections(Long uid);

Map<Long,List<IMConn>> getUserWscNodeIds(Set<Long> uidSet);

String  writeHZkey(String key,String value);

String readHZkey(String key);


}
