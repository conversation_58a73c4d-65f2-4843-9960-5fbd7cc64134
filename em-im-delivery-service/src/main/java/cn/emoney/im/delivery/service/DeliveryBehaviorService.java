package cn.emoney.im.delivery.service;

import java.time.LocalDate;
import java.util.Map;
import java.util.Set;


/**
 * 消息投递行为服务接口类
 * <AUTHOR>
 * @date 2024/01/08 11:45
 **/
public interface DeliveryBehaviorService {

    //批量获取用户今日某业务使用次数
    Map<Long, Integer> getUserTodayBusinessCount(String bizkey, Set<Long> uidSet, LocalDate date);

    //批量设置用户今日某业务使用次数+1
    void setUserTodayBusinessCount(String bizkey, Set<Long> uidSet, LocalDate date);

}
