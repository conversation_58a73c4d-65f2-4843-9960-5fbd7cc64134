package cn.emoney.im.api.delivery.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum TargetType {

        /**全局目标*/
        GLOBAL(0),

        /**单个用户目标*/
        SINGLE_USER(1),

        /**群组目标*/
        GROUP(2),

        /**群发多个用户*/
        BATCH_USER(3),

        /**群发多个群组*/
        BATCH_GROUP(4);

        @JsonValue
        private final int value;

        @JsonCreator
        TargetType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

}
