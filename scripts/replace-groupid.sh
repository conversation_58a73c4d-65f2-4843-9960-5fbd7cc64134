#!/bin/bash

# 私有化部署GroupId替换脚本
# 用法: ./replace-groupid.sh <customer>
# 支持的客户: xc (湘财), gt (国泰居安)

set -e

CUSTOMER=$1

if [ -z "$CUSTOMER" ]; then
    echo "错误: 请指定客户代码"
    echo "用法: $0 <customer>"
    echo "支持的客户: xc (湘财), gt (国泰居安)"
    exit 1
fi

# 定义GroupId映射
case $CUSTOMER in
    "xc")
        OLD_GROUPID="cn.emoney.im"
        NEW_GROUPID="com.xc.im"
        CUSTOMER_NAME="湘财"
        ;;
    "gt") 
        OLD_GROUPID="cn.emoney.im"
        NEW_GROUPID="com.gtja.im"
        CUSTOMER_NAME="国泰居安"
        ;;
    *)
        echo "错误: 不支持的客户代码: $CUSTOMER"
        echo "支持的客户: xc (湘财), gt (国泰居安)"
        exit 1
        ;;
esac

echo "开始为客户 $CUSTOMER_NAME 替换GroupId..."
echo "从 $OLD_GROUPID 替换为 $NEW_GROUPID"

# 备份原始POM文件
echo "备份原始POM文件..."
find . -name "pom.xml" -exec cp {} {}.backup \;

# 替换根POM文件中的GroupId
echo "替换根POM文件..."
if [ -f "pom-${CUSTOMER}.xml" ]; then
    cp "pom-${CUSTOMER}.xml" pom.xml
    echo "使用客户专用POM配置: pom-${CUSTOMER}.xml"
else
    echo "警告: 未找到客户专用POM配置文件 pom-${CUSTOMER}.xml"
    # 直接替换现有POM中的GroupId
    sed -i "s|<groupId>${OLD_GROUPID}</groupId>|<groupId>${NEW_GROUPID}</groupId>|g" pom.xml
fi

# 替换子模块POM文件中的GroupId
echo "替换子模块POM文件..."
find . -path "./em-im-*" -name "pom.xml" -exec sed -i "s|<groupId>${OLD_GROUPID}</groupId>|<groupId>${NEW_GROUPID}</groupId>|g" {} \;

# 替换依赖中的GroupId
echo "替换依赖中的GroupId..."
find . -name "pom.xml" -exec sed -i "s|<groupId>${OLD_GROUPID}</groupId>|<groupId>${NEW_GROUPID}</groupId>|g" {} \;

echo "GroupId替换完成!"
echo "请检查以下文件的修改:"
find . -name "pom.xml" -exec echo "  - {}" \;

echo ""
echo "如需恢复原始配置，请运行:"
echo "find . -name \"pom.xml.backup\" -exec bash -c 'mv \"\$1\" \"\${1%.backup}\"' _ {} \;"
