package cn.emoney.im.delivery.manager;

import cn.emoney.im.delivery.entity.DeliveryRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 功能描述:
 * Tcp-Websocket连接服务管理类
 * @Param:
 * @Return:
 * @Author: tengdengming
 * @Date: 2024/1/15 11:42
 */
@FeignClient(name = "wsc", url = "${feign.client.wsc.url}" ,contextId = "wsc")
public interface WebsocketManager {

    /**
     * Pushes a message using the given DeliveryRequest.
     * @param  body  the DeliveryRequest to be pushed
     * @return      a ResponseEntity containing a String
     */
    @PostMapping("postmessage")
    ResponseEntity<String> pushMessage(@RequestBody DeliveryRequest body);

    /**
     * Sends a message to a node.
     * @param  body     the delivery request body
     * @param  nodeId   the node ID
     * @return          a response entity containing a string
     */
    @PostMapping(value = "postmessage")
    ResponseEntity<String> sendMessage2Node(@RequestBody DeliveryRequest body,@RequestHeader("nodeId") Integer nodeId);

}

