package cn.emoney.im.api.delivery.client;

import cn.emoney.im.api.delivery.dto.DeliveryResponse;
import cn.emoney.im.api.delivery.dto.TcpMessage;
import cn.emoney.im.api.delivery.exception.DeliveryErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        // 全局唯一标识, 为空时会使用 name 作为 contextId
        //contextId = "im-delivery",
        // 服务名 与被调用方的 spring.application.name 一致
        name = "im-delivery",
        // 服务地址 用于覆盖 服务发现的地址
        url = "${em-im.service.im-delivery:}"
        // 模块路径 本配置最终会变成 lb://im-demo/demo/*** 或 http://url/demo/*** 调用
        //path = "",
        //configuration = {ImFeignClientConfig.class}
        )
public interface DeliveryServiceFeignClient {

    /**
     * 功能描述:
     * 投递TcpMessage
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2024/1/15 18:03
     */
    @PostMapping("deliveryMessage")
    DeliveryResponse deliveryMessage(@RequestBody TcpMessage tcpMessage);

    @PostMapping("deliverBatchUserTcpMessage")
    DeliveryResponse deliverBatchUserTcpMessage(@RequestBody List<TcpMessage> tcpMessageList);


    /**
     * 增加忽略记录发送次数的发送方法
     * @param  tcpMessageList description of parameter
     * @return         	description of return value
     */
    @PostMapping("deliverBatchUserTcpMessageIgnoreCount")
    DeliveryResponse deliverBatchUserTcpMessageIgnoreCount(@RequestBody List<TcpMessage> tcpMessageList);

    /**
     * 添加批量群组发送方法 2025年6月18日
     * @param  tcpMessageList description of parameter
     * @return         	description of return value
     */
    @PostMapping("deliverBatchGroupTcpMessage")
    DeliveryResponse deliverBatchGroupTcpMessage(@RequestBody List<TcpMessage> tcpMessageList);

}
