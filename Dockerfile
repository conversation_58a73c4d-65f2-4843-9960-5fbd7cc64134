# syntax=office-docker-mirror.emoney.cn/docker/dockerfile:1.4
FROM office-docker-mirror.emoney.cn/maven:3.9.5-eclipse-temurin-21-alpine as build-stage
RUN mkdir -p /root/.m2/repository
ADD https://officerepo.emoney.cn/setting/maven/settings.xml /root/.m2
WORKDIR /build/
COPY . .
ARG MAVEN_CLI_OPTS="--batch-mode \
--errors \
--fail-at-end \
--show-version \
--no-transfer-progress"
RUN --mount=type=cache,target=/root/.m2/repository \
mvn package -U -Dmaven.test.skip -Dmaven.source.skip ${MAVEN_CLI_OPTS}

FROM office-docker-mirror.emoney.cn/eclipse-temurin:21-jre-alpine as image
WORKDIR /app/
ARG module=em-im-delivery-service
ENV SPRING_PROFILES_ACTIVE dev
ENV TZ Asia/Shanghai
# skywalking start
ENV SW_AGENT_NAME ${module}
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES ************:11800,************:11800,************:11800
ENV SW_KAFKA_BOOTSTRAP_SERVERS ************:9092,************:9092,************:9092
COPY --link --from=office-docker-mirror.emoney.cn/apache/skywalking-java-agent:9.1.0-alpine \
/skywalking/agent ./skywalking/agent
COPY --link --from=office-docker-mirror.emoney.cn/apache/skywalking-java-agent:9.1.0-alpine \
/skywalking/agent/optional-reporter-plugins/kafka-reporter-plugin* \
./skywalking/agent/plugins/
ENV JAVA_TOOL_OPTIONS -javaagent:./skywalking/agent/skywalking-agent.jar
# skywalking end
ENV _JAVA_OPTIONS -Xms6144m -Xmx6144m -Xss2m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
-XX:ParallelGCThreads=16 -XX:ConcGCThreads=4 \
-Xlog:gc*:/opt/app-gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/app.dump
COPY --link --from=build-stage /build/${module}/target/*.jar ./app.jar
EXPOSE 8080
HEALTHCHECK --start-period=10s --timeout=3s \
  CMD nc -zv -w 2 localhost 8080 || exit 1
#ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -jar app.jar
# 在 ENTRYPOINT 行中添加启动参数
ENTRYPOINT ["java", \
            "--add-opens=java.base/jdk.internal.access=ALL-UNNAMED", \
            "--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED", \
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED", \
            "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED", \
            "--add-opens=java.management/com.sun.jmx.mbeanserver=ALL-UNNAMED", \
            "--add-opens=jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED", \
            "--add-opens=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED", \
            "--add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED", \
            "--add-opens=java.base/java.io=ALL-UNNAMED", \
            "--add-opens=java.base/java.nio=ALL-UNNAMED", \
            "--add-opens=java.base/java.net=ALL-UNNAMED", \
            "--add-opens=java.base/java.util=ALL-UNNAMED", \
            "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED", \
            "--add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED", \
            "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED", \
            "--add-opens=java.base/java.lang=ALL-UNNAMED", \
            "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED", \
            "--add-opens=java.base/java.math=ALL-UNNAMED", \
            "--add-opens=java.sql/java.sql=ALL-UNNAMED", \
            "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED", \
            "--add-opens=java.base/java.time=ALL-UNNAMED", \
            "--add-opens=java.base/java.text=ALL-UNNAMED", \
            "--add-opens=java.management/sun.management=ALL-UNNAMED", \
            "--add-opens=java.desktop/java.awt.font=ALL-UNNAMED", \

            "--add-modules", "java.se", \
            "--add-exports", "java.base/jdk.internal.ref=ALL-UNNAMED", \
            "--add-opens", "java.base/java.lang=ALL-UNNAMED", \
            "--add-opens", "java.base/sun.nio.ch=ALL-UNNAMED", \
            "--add-opens", "java.management/sun.management=ALL-UNNAMED", \
            "--add-opens", "jdk.management/com.sun.management.internal=ALL-UNNAMED", \

            "-Djava.net.preferIPv4Stack=true", \
            #"-Dserver.http2.max-concurrent-streams=300",
            "-Djava.security.egd=file:/dev/./urandom", \
            "-jar", "app.jar"]