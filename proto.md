# 使用 ProtoBuf

1. 安装 protoc

   https://github.com/protocolbuffers/protobuf/releases

2. 引入依赖
   ```xml
   <dependency>
       <groupId>com.google.protobuf</groupId>
       <artifactId>protobuf-java</artifactId>
   </dependency>
   ```

3. 在 proto 目录下创建 proto 文件，如 `proto/demo.proto`

4. 执行以下命令生成 Java 代码
   ```shell
   protoc --java_out=em-im-demo-api\src\main\java\ em-im-demo-api\src\main\resources\proto\*.proto
   ```