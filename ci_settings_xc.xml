<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 http://maven.apache.org/xsd/settings-1.2.0.xsd">
    <mirrors>
        <mirror>
            <id>xc-public</id>
            <mirrorOf>central</mirrorOf>
            <url>https://nexus.xc.com/maven-public/</url>
        </mirror>
    </mirrors>

    <profiles>
        <profile>
            <id>xc-public</id>
            <repositories>
                <repository>
                    <id>xc-public</id>
                    <url>https://nexus.xc.com/maven-public/</url>
                    <layout>default</layout>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>xc-public</id>
                    <url>https://nexus.xc.com/maven-public/</url>
                    <layout>default</layout>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>xc-public</activeProfile>
    </activeProfiles>

    <servers>
        <server>
            <id>xc-releases</id>
            <username>${CI_JOB_USERNAME}</username>
            <password>${CI_JOB_PASSWORD}</password>
        </server>
        <server>
            <id>xc-snapshots</id>
            <username>${CI_JOB_USERNAME}</username>
            <password>${CI_JOB_PASSWORD}</password>
        </server>
    </servers>
</settings>
