package cn.emoney.im.api.delivery.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * LinkServer Command Request Back
 *
 * <AUTHOR>
 * @date 2024/04/28 16:12
 **/
@Data
public class CmdResponse {

    //constructor
    public CmdResponse() {
        this.requestTime = LocalDateTime.now();
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime requestTime;

    public String receiptNo;

    public String commandDetail;

    public String commandType;

    public Integer code;

}
